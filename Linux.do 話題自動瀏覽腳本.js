// ==UserScript==
// @name        Linux.do 話題自動瀏覽腳本
// @namespace   LinuxdoTools
// @match       *://linux.do/*
// @grant       GM_xmlhttpRequest
// @grant       GM_cookie
// @connect     linux.do
// @run-at      document-idle
// @version     1.0
// <AUTHOR>
// @description Linux.do 論壇話題自動瀏覽工具，依序瀏覽話題並停留指定時間
// ==/UserScript==

/**
 * Linux.do 論壇話題自動瀏覽腳本
 * 功能：自動依序瀏覽話題，每個話題停留指定時間
 */

// 全局設置
const DEBUG_MODE = true;
const TOPIC_STAY_TIME = 10000; // 每個話題停留時間 (毫秒)

// 全局控制變數
let isRunning = false;
let currentTopicId = null;
let processingDirection = "+";
let processingStep = 1;
let processedCount = 0;
let loginStatus = false;
let nextProcessTimeout = null;

// 添加新的配置項
const CONFIG = {
  article: {
    topicListLimit: 100,  // 一次獲取的話題數量限制
    retryLimit: 3         // 重試次數限制
  }
};

// 增強調試日誌函數
function debugLog(...args) {
  if (DEBUG_MODE) {
    const timestamp = new Date().toLocaleTimeString();
    console.log(`[LinuxDo自動瀏覽 ${timestamp}]`, ...args);
  }
}

// 等待頁面加載完成
function waitForPageLoad(timeout = 5000) {
  return new Promise((resolve) => {
    if (document.readyState === 'complete') {
      resolve();
      return;
    }

    const timer = setTimeout(() => {
      debugLog('頁面加載超時，繼續執行');
      resolve();
    }, timeout);

    window.addEventListener('load', () => {
      clearTimeout(timer);
      resolve();
    }, { once: true });
  });
}

// 安全的元素選擇器
function safeQuerySelector(selector, parent = document) {
  try {
    if (!parent || typeof parent.querySelector !== 'function') {
      debugLog('無效的父元素:', parent);
      return null;
    }
    return parent.querySelector(selector);
  } catch (error) {
    debugLog('選擇器錯誤:', selector, error);
    return null;
  }
}

// 安全的元素選擇器（多個）
function safeQuerySelectorAll(selector, parent = document) {
  try {
    if (!parent || typeof parent.querySelectorAll !== 'function') {
      debugLog('無效的父元素:', parent);
      return [];
    }
    return Array.from(parent.querySelectorAll(selector));
  } catch (error) {
    debugLog('選擇器錯誤:', selector, error);
    return [];
  }
}

// 檢查登入狀態
async function checkLoginStatus(showAlert = true) {
  try {
    // 等待頁面完全加載
    await waitForPageLoad();

    // 方法1: 頁面元素檢測
    const currentUserElement = safeQuerySelector('.current-user');
    const method1 = currentUserElement !== null;

    // 方法2: 檢查用戶菜單
    const userMenuElement = safeQuerySelector('.header-dropdown-toggle.current-user');
    const method3 = userMenuElement !== null;

    // 方法3: 檢查是否有用戶頭像
    const avatarElement = safeQuerySelector('img.avatar');
    const method4 = avatarElement !== null;

    // 方法4: 檢查用戶信息API
    let method5 = false;
    try {
      const response = await fetch('/session/current.json');
      if (response.ok) {
        const data = await response.json();
        method5 = data && data.current_user && data.current_user.id;
      }
    } catch (apiError) {
      debugLog("API檢查登入狀態失敗:", apiError);
    }

    // 綜合判斷
    loginStatus = method1 || method3 || method4 || method5;

    debugLog("登入狀態檢測結果:", {
      method1,
      method3,
      method4,
      method5,
      overall: loginStatus,
      currentUserElement: !!currentUserElement,
      userMenuElement: !!userMenuElement,
      avatarElement: !!avatarElement
    });

    if (!loginStatus && showAlert) {
      alert("未檢測到登入狀態。請先登入 Linux.do 論壇。");
    }

    return loginStatus;
  } catch (error) {
    console.error("檢查登入狀態時出錯:", error);
    return false;
  }
}

// 添加獲取最新話題列表的函數
async function getLatestTopics() {
  let page = 1;
  let topicList = [];
  let retryCount = 0;

  debugLog('開始獲取最新話題列表');

  while (topicList.length < CONFIG.article.topicListLimit && retryCount < CONFIG.article.retryLimit) {
    try {
      debugLog(`正在獲取第 ${page} 頁話題`);

      // 添加超時控制
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);

      const response = await fetch(`https://linux.do/latest.json?no_definitions=true&page=${page}`, {
        signal: controller.signal,
        headers: {
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        }
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data?.topic_list?.topics && Array.isArray(data.topic_list.topics)) {
        const topics = data.topic_list.topics;
        topicList.push(...topics);
        debugLog(`成功獲取第 ${page} 頁，共 ${topics.length} 個話題`);
        page++;

        // 如果返回的話題數量少於預期，可能已經到達最後一頁
        if (topics.length < 30) {
          debugLog('話題數量較少，可能已到達最後一頁');
          break;
        }
      } else {
        debugLog('沒有更多話題了或數據格式異常');
        break;
      }
    } catch (error) {
      console.error('獲取話題列表失敗:', error);
      retryCount++;

      if (error.name === 'AbortError') {
        debugLog('請求超時，正在重試...');
      }

      // 增加重試延遲
      await new Promise(resolve => setTimeout(resolve, Math.min(1000 * retryCount, 5000)));
    }
  }

  debugLog(`總共獲取到 ${topicList.length} 個話題`);
  return topicList;
}

// 修改 startProcessing 函數
async function startProcessing() {
  // 檢查登入狀態
  if (!await checkLoginStatus()) {
    return;
  }

  // 已經在運行中，不重新啟動
  if (isRunning) {
    alert("已有瀏覽任務運行中");
    return;
  }

  // 讓用戶選擇模式
  const mode = prompt("請選擇瀏覽模式：\n1 = 從最新話題開始\n2 = 自定義起始話題", "1");
  if (!mode || !["1", "2"].includes(mode)) return;

  try {
    if (mode === "1") {
      // 從最新話題開始的模式
      showFloatingStatus("正在獲取最新話題列表...");
      const topicList = await getLatestTopics();

      if (topicList.length === 0) {
        alert("無法獲取話題列表");
        return;
      }

      currentTopicId = topicList[0].id;
      processingDirection = "-"; // 從新到舊遍歷
      processingStep = 1;
    } else {
      // 自定義模式
      const startTopicId = prompt("請輸入起始話題ID:", "1");
      if (startTopicId === null) return;

      if (!/^\d+$/.test(startTopicId.trim())) {
        alert("請輸入有效的數字ID");
        return;
      }

      const directionInput = prompt("請選擇瀏覽方向 (+遞增 或 -遞減):", "+");
      if (directionInput === null) return;

      processingDirection = directionInput.trim() === "-" ? "-" : "+";

      const stepInput = prompt("請輸入話題ID間隔（等差值）:", "1");
      if (stepInput === null) return;

      if (!/^\d+$/.test(stepInput.trim()) || parseInt(stepInput.trim()) < 1) {
        alert("請輸入大於或等於1的正整數");
        return;
      }

      currentTopicId = parseInt(startTopicId.trim());
      processingStep = parseInt(stepInput.trim());
    }

    // 重置計數器並開始運行
    processedCount = 0;
    isRunning = true;

    debugLog(`開始瀏覽話題，起始ID: ${currentTopicId}, 方向: ${processingDirection}, 步進值: ${processingStep}`);
    showFloatingStatus(`開始瀏覽話題，從ID: ${currentTopicId} 開始`);

    // 導航到第一個話題
    navigateToTopic(currentTopicId);
  } catch (error) {
    console.error("初始化瀏覽任務時出錯:", error);
    alert("初始化失敗，請稍後再試");
  }
}

// 導航到指定話題頁面
function navigateToTopic(topicId) {
  if (!isRunning) {
    debugLog("瀏覽已停止，取消導航");
    return;
  }

  debugLog(`準備導航到話題 ${topicId}`, {
    currentUrl: window.location.href,
    targetUrl: `https://linux.do/t/topic/${topicId}`
  });

  showFloatingStatus(`正在導航到話題: ${topicId}，已處理: ${processedCount}`);

  // 添加延遲以確保日誌輸出
  setTimeout(() => {
    debugLog(`即將導航到話題: ${topicId}`);
    window.location.href = `https://linux.do/t/topic/${topicId}`;
  }, 100);
}

// 顯示漂浮狀態提示
function showFloatingStatus(message) {
  try {
    // 檢查是否已存在狀態元素
    let statusElement = safeQuerySelector('#auto-browse-status');

    if (!statusElement) {
      // 創建新的狀態元素
      statusElement = document.createElement('div');
      statusElement.id = 'auto-browse-status';
      statusElement.style.cssText = `
        position: fixed;
        bottom: 10px;
        right: 10px;
        background-color: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 10px 15px;
        border-radius: 5px;
        font-size: 14px;
        z-index: 10000;
        max-width: 300px;
        word-wrap: break-word;
        box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        font-family: Arial, sans-serif;
      `;

      if (document.body) {
        document.body.appendChild(statusElement);
      } else {
        // 如果body還沒準備好，等待一下
        setTimeout(() => showFloatingStatus(message), 100);
        return;
      }
    }

    // 更新狀態信息
    if (statusElement) {
      statusElement.textContent = message;
      debugLog('狀態更新:', message);
    }
  } catch (error) {
    debugLog('顯示狀態提示時出錯:', error);
  }
}

// 處理下一個話題
function processNextTopic() {
  debugLog('開始處理下一個話題');

  if (!isRunning) {
    debugLog("瀏覽已停止，取消處理");
    return;
  }

  // 更新計數器
  processedCount++;
  debugLog(`已處理話題數: ${processedCount}`);

  // 計算下一個話題ID
  const oldTopicId = currentTopicId;
  if (processingDirection === "+") {
    currentTopicId += processingStep;
  } else {
    currentTopicId -= processingStep;
    if (currentTopicId < 1) {
      debugLog("已達到最小ID 1，停止瀏覽");
      isRunning = false;
      showFloatingStatus("已達到最小ID 1，自動瀏覽已停止");
      return;
    }
  }

  debugLog('話題ID更新', {
    oldId: oldTopicId,
    newId: currentTopicId,
    direction: processingDirection,
    step: processingStep
  });

  // 導航到下一個話題
  navigateToTopic(currentTopicId);
}

// 停止瀏覽
function stopProcessing() {
  if (isRunning) {
    isRunning = false;
    debugLog("已手動停止瀏覽");

    // 清除任何待處理的超時
    if (nextProcessTimeout) {
      clearTimeout(nextProcessTimeout);
      nextProcessTimeout = null;
    }

    showFloatingStatus("自動瀏覽已停止");
  }
}

// 創建控制按鈕
function createControlButtons() {
  try {
    // 檢查是否已存在按鈕容器
    if (safeQuerySelector('#auto-browse-controls')) {
      debugLog('控制按鈕已存在，跳過創建');
      return;
    }

    // 等待body元素可用
    if (!document.body) {
      setTimeout(createControlButtons, 100);
      return;
    }

    // 創建主按鈕容器
    const buttonContainer = document.createElement('div');
    buttonContainer.id = 'auto-browse-controls';
    buttonContainer.style.cssText = `
      position: fixed;
      top: 70px;
      right: 20px;
      z-index: 10000;
      display: flex;
      flex-direction: column;
      gap: 10px;
      font-family: Arial, sans-serif;
    `;

    // 創建開始按鈕
    const startButton = document.createElement('button');
    startButton.textContent = '🚀 開始自動瀏覽';
    startButton.style.cssText = `
      padding: 8px 15px;
      background-color: #28a745;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.2s;
    `;
    startButton.addEventListener('click', startProcessing);
    startButton.addEventListener('mouseenter', () => {
      startButton.style.backgroundColor = '#218838';
    });
    startButton.addEventListener('mouseleave', () => {
      startButton.style.backgroundColor = '#28a745';
    });
    buttonContainer.appendChild(startButton);

    // 創建停止按鈕
    const stopButton = document.createElement('button');
    stopButton.textContent = '⏹️ 停止瀏覽';
    stopButton.style.cssText = `
      padding: 8px 15px;
      background-color: #dc3545;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.2s;
    `;
    stopButton.addEventListener('click', stopProcessing);
    stopButton.addEventListener('mouseenter', () => {
      stopButton.style.backgroundColor = '#c82333';
    });
    stopButton.addEventListener('mouseleave', () => {
      stopButton.style.backgroundColor = '#dc3545';
    });
    buttonContainer.appendChild(stopButton);

    document.body.appendChild(buttonContainer);
    debugLog('控制按鈕創建成功');
  } catch (error) {
    debugLog('創建控制按鈕時出錯:', error);
  }
}

// 保存自動瀏覽狀態到 localStorage
function saveState() {
  const state = {
    isRunning,
    currentTopicId,
    processingDirection,
    processingStep,
    processedCount,
    timestamp: Date.now()
  };
  localStorage.setItem('autoBrowseState', JSON.stringify(state));
  debugLog('已保存狀態:', state);
}

// 從 localStorage 恢復自動瀏覽狀態
function loadState() {
  const savedState = localStorage.getItem('autoBrowseState');
  if (savedState) {
    try {
      const state = JSON.parse(savedState);
      // 檢查狀態是否在60秒內保存的，如果超過則視為過期
      const timeDiff = Date.now() - state.timestamp;
      debugLog('狀態時間差:', timeDiff, 'ms');

      if (timeDiff < 60000) { // 60秒內的狀態視為有效
        isRunning = state.isRunning || false;
        currentTopicId = state.currentTopicId || null;
        processingDirection = state.processingDirection || "+";
        processingStep = state.processingStep || 1;
        processedCount = state.processedCount || 0;
        debugLog('已恢復狀態:', {
          isRunning,
          currentTopicId,
          processingDirection,
          processingStep,
          processedCount,
          timeDiff
        });
        return true;
      } else {
        debugLog('狀態已過期，清除舊狀態');
        clearState();
      }
    } catch (error) {
      console.error('恢復狀態時出錯:', error);
      clearState();
    }
  }
  return false;
}

// 清除保存的狀態
function clearState() {
  localStorage.removeItem('autoBrowseState');
  debugLog('已清除保存的狀態');
}

// 檢查當前頁面是否為話題頁面
function isTopicPage() {
  return window.location.pathname.match(/^\/t\/topic\/\d+/);
}

// 從URL獲取當前話題ID
function getCurrentTopicIdFromUrl() {
  const match = window.location.pathname.match(/^\/t\/topic\/(\d+)/);
  return match ? parseInt(match[1]) : null;
}

// 檢查頁面是否為404或錯誤頁面
function is404Page() {
  try {
    // 檢查URL中是否包含404
    if (window.location.href.includes('/404')) return true;

    // 檢查頁面標題是否包含404或錯誤相關詞彙
    const title = document.title || '';
    if (title.includes('404') || title.includes('Not Found') || title.includes('錯誤') || title.includes('Error')) {
      return true;
    }

    // 檢查是否有404特定元素
    const errorSelectors = [
      '.page-not-found',
      '.error-page',
      '.not-found',
      '[data-error="404"]',
      '.ember-error'
    ];

    for (const selector of errorSelectors) {
      if (safeQuerySelector(selector)) {
        return true;
      }
    }

    // 檢查頁面內容是否包含錯誤信息
    const bodyText = document.body ? document.body.textContent || '' : '';
    if (bodyText.includes('頁面不存在') || bodyText.includes('Page not found') || bodyText.includes('找不到頁面')) {
      return true;
    }

    return false;
  } catch (error) {
    debugLog('檢查404頁面時出錯:', error);
    return false;
  }
}

// 修改 handleTopicPage 函數，添加更多錯誤處理
async function handleTopicPage() {
  try {
    // 等待頁面穩定
    await new Promise(resolve => setTimeout(resolve, 1000));

    const urlTopicId = getCurrentTopicIdFromUrl();
    debugLog('當前頁面話題ID:', urlTopicId);
    debugLog('當前運行狀態:', {
      isRunning,
      currentTopicId,
      processedCount,
      nextTimeout: !!nextProcessTimeout,
      is404: is404Page(),
      pageTitle: document.title,
      url: window.location.href
    });

    // 檢查是否為404頁面或其他錯誤
    if (is404Page()) {
      debugLog(`話題 ${currentTopicId} 不存在或無法訪問，跳到下一個`);
      showFloatingStatus(`話題 ${currentTopicId} 不可訪問，即將跳到下一個`);

      setTimeout(() => {
        processNextTopic();
      }, 2000);
      return;
    }

    // 檢查頁面是否正常加載
    if (!urlTopicId) {
      debugLog('無法從URL獲取話題ID，可能頁面未正確加載');
      showFloatingStatus('頁面加載異常，即將重試');

      setTimeout(() => {
        if (isRunning) {
          navigateToTopic(currentTopicId);
        }
      }, 3000);
      return;
    }

    if (isRunning && urlTopicId === currentTopicId) {
      debugLog(`設置 ${TOPIC_STAY_TIME}ms 後切換到下一個話題`);
      showFloatingStatus(`正在瀏覽話題 ${currentTopicId}，已處理: ${processedCount} | 10秒後切換下一個`);

      // 清除之前的計時器
      if (nextProcessTimeout) {
        clearTimeout(nextProcessTimeout);
      }

      nextProcessTimeout = setTimeout(() => {
        debugLog('計時器觸發，準備切換到下一個話題');
        processNextTopic();
      }, TOPIC_STAY_TIME);
    } else {
      debugLog('不符合處理條件，跳過當前頁面', {
        isRunning,
        urlTopicId,
        currentTopicId,
        match: urlTopicId === currentTopicId
      });
    }
  } catch (error) {
    console.error('處理話題頁面時出錯:', error);
    showFloatingStatus('處理頁面時出錯，即將重試');

    setTimeout(() => {
      if (isRunning) {
        processNextTopic();
      }
    }, 3000);
  }
}

// 頁面加載完成時的處理
async function onPageLoad() {
  try {
    debugLog('頁面加載完成，初始化腳本');
    debugLog('當前頁面URL:', window.location.href);
    debugLog('頁面標題:', document.title);
    debugLog('用戶代理:', navigator.userAgent);

    // 等待頁面完全穩定
    await waitForPageLoad();

    // 延遲一點時間確保頁面完全渲染
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 創建控制按鈕
    createControlButtons();
    debugLog('控制按鈕創建完成');

    // 嘗試恢復狀態
    const stateRestored = loadState();
    debugLog('狀態恢復結果:', {
      restored: stateRestored,
      currentState: {
        isRunning,
        currentTopicId,
        processingDirection,
        processingStep,
        processedCount
      }
    });

    if (stateRestored && isRunning) {
      // 如果在運行中，顯示恢復狀態
      showFloatingStatus(`已恢復瀏覽任務，當前話題: ${currentTopicId}，已處理: ${processedCount}`);

      // 如果當前是話題頁面，則處理當前頁面
      const isTopicPageResult = isTopicPage();
      debugLog('頁面檢查結果:', {
        isTopicPage: isTopicPageResult,
        pathname: window.location.pathname,
        href: window.location.href
      });

      if (isTopicPageResult) {
        debugLog('符合處理條件，開始處理當前頁面');
        await handleTopicPage();
      } else {
        // 如果不是話題頁面，導航到當前應該處理的話題
        debugLog('當前不是話題頁面，導航到目標話題');
        navigateToTopic(currentTopicId);
      }
    }

    // 設置狀態保存
    window.addEventListener('beforeunload', () => {
      debugLog('頁面即將卸載，保存狀態');
      try {
        if (isRunning) {
          saveState();
          debugLog('運行狀態已保存');
        } else {
          clearState();
          debugLog('狀態已清除');
        }
      } catch (error) {
        debugLog('保存狀態時出錯:', error);
      }
    });

    debugLog('腳本初始化完成');
  } catch (error) {
    console.error('頁面加載處理時出錯:', error);
    debugLog('初始化失敗，但腳本將繼續運行');
  }
}

// 當頁面加載完成時初始化
try {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', onPageLoad);
    debugLog('等待DOM加載完成');
  } else {
    // 延遲執行以確保其他腳本不會干擾
    setTimeout(onPageLoad, 500);
    debugLog('DOM已加載，延遲初始化');
  }
} catch (error) {
  console.error('腳本初始化失敗:', error);
  // 即使出錯也嘗試初始化
  setTimeout(onPageLoad, 1000);
}